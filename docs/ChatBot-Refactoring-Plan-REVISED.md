# ChatBot Navigation Refactoring Plan - REVISADO

## 1. Análise do Estado Atual

### 1.1 Problemas Identificados

**PROBLEMA PRINCIPAL:** Navegação inconsistente e duplicada
- `ConditionalNavigationService` funciona mas está limitado
- `FlowNavigationService` criado mas incompleto e confuso
- `ProcessWebhookMessage` modificado de forma inadequada
- Lógica de navegação espalhada em múltiplos lugares

**PROBLEMAS ESPECÍFICOS:**
1. `FlowNavigationService.handleConditionalButtonNavigation()` não implementado
2. Mistura de responsabilidades no `ProcessWebhookMessage`
3. Navegação por `position` vs `step_id` inconsistente
4. Processors funcionam mas não usam navegação unificada

### 1.2 Estado Atual dos Processors

**FUNCIONANDO:**
- `MessageStepProcessor` - Exibe mensagem e avança
- `InteractiveStepProcessor` - Mostra botões/listas
- `InputStepProcessor` - Coleta entrada do usuário
- `CommandStepProcessor` - Executa lógica de negócio
- `ConditionStepProcessor` - Avaliação condicional
- `WebhookStepProcessor` - Chama APIs externas
- `DelayStepProcessor` - Introduz delays

**PROBLEMA:** Cada processor tem sua própria lógica de navegação

## 2. Plano de Refatoração REALISTA

### OBJETIVO: Unificar navegação sem quebrar o que funciona

### PASSO 1: Corrigir FlowNavigationService
**Tempo:** 2 horas
**Objetivo:** Fazer o service funcionar corretamente

#### 1.1 Implementar handleConditionalButtonNavigation
```php
protected function handleConditionalButtonNavigation($button, Step $currentStep, array $context = []): ?Step
{
    $targetStepIdentifier = $button->getTargetStepIdentifier();
    if (!$targetStepIdentifier) {
        return $this->getDefaultNextStep($currentStep);
    }
    
    $targetStepId = $this->resolveStepIdentifier($targetStepIdentifier, $currentStep);
    if (!$targetStepId) {
        return $this->getDefaultNextStep($currentStep);
    }
    
    return $this->stepRepository->fetchById($targetStepId);
}
```

#### 1.2 Corrigir getNextStepFromButtonNavigation
```php
protected function getNextStepFromButtonNavigation(Step $currentStep, string $userInput, array $context = []): ?Step
{
    if (!$currentStep->component || !$currentStep->component->buttons) {
        return null;
    }

    foreach ($currentStep->component->buttons as $button) {
        if ($this->buttonMatchesInput($button, $userInput)) {
            if ($button->internal_type === 'condition') {
                return $this->handleConditionalButtonNavigation($button, $currentStep, $context);
            }
        }
    }

    return null;
}
```

### PASSO 2: Reverter ProcessWebhookMessage
**Tempo:** 30 minutos
**Objetivo:** Voltar ao estado funcional original

#### 2.1 Remover modificações confusas
- Remover parâmetro `$conversation` do método `perform()`
- Remover método `performLegacy()`
- Voltar à responsabilidade única: extrair dados do webhook

#### 2.2 Manter apenas extração de dados
```php
public function perform(array $webhookData): array
{
    // Apenas extração e validação de dados do webhook
    // SEM lógica de navegação
}
```

### PASSO 3: Integrar FlowNavigationService nos Processors
**Tempo:** 3 horas
**Objetivo:** Usar navegação unificada onde faz sentido

#### 3.1 Modificar InteractiveStepProcessor
```php
class InteractiveStepProcessor implements StepProcessorInterface
{
    protected FlowNavigationService $flowNavigationService;
    
    protected function getNextStepForSelection(Step $step, string $selection): ?int
    {
        // Usar FlowNavigationService em vez de lógica própria
        $nextStep = $this->flowNavigationService->getNextStep($step, $selection, []);
        return $nextStep?->id;
    }
}
```

#### 3.2 Manter outros processors como estão
- `MessageStepProcessor` - OK como está
- `InputStepProcessor` - OK como está  
- `CommandStepProcessor` - OK como está
- `ConditionStepProcessor` - OK como está
- `WebhookStepProcessor` - OK como está
- `DelayStepProcessor` - OK como está

### PASSO 4: Substituir ConditionalNavigationService
**Tempo:** 1 hora
**Objetivo:** Usar FlowNavigationService no ProcessFlowStep

#### 4.1 Modificar ProcessFlowStep
```php
protected function applyConditionalNavigation(
    array $stepResult,
    Step $currentStep,
    WhatsAppInteraction $interaction
): array {
    if (!($stepResult['move_to_next'] ?? false)) {
        return $stepResult;
    }

    // Usar FlowNavigationService em vez de ConditionalNavigationService
    $nextStep = $this->flowNavigationService->getNextStep(
        $currentStep,
        $interaction->content ?? '',
        ['conversation' => $interaction->conversation]
    );

    if ($nextStep) {
        $stepResult['next_step'] = $nextStep->id;
    }

    return $stepResult;
}
```

## 3. Explicação Detalhada por Tipo de Step

### 3.1 MESSAGE Step
**Função:** Exibir mensagem e avançar automaticamente
**Navegação:** Sempre usa `next_step` (position → step_id)
**Não precisa de FlowNavigationService:** Navegação é sempre linear

### 3.2 INTERACTIVE Step  
**Função:** Mostrar botões/listas e aguardar seleção
**Navegação:** Baseada na seleção do usuário
**USA FlowNavigationService:** Para processar botões condicionais

**Fluxo:**
1. Mostra opções para o usuário
2. Usuário clica em botão
3. FlowNavigationService determina próximo step baseado no botão
4. Se botão tem `internal_type='condition'`, usa `target_step_identifier`
5. Senão, usa `next_step` padrão

### 3.3 INPUT Step
**Função:** Coletar entrada do usuário (nome, email, etc.)
**Navegação:** Sempre linear após validação
**Não precisa de FlowNavigationService:** Navegação é sempre `next_step`

**Fluxo:**
1. Solicita entrada do usuário
2. Valida entrada
3. Armazena em `client.campo` usando field_mapping
4. Avança para `next_step`

### 3.4 COMMAND Step
**Função:** Executar lógica de negócio (atualizar cliente, calcular, etc.)
**Navegação:** Pode ser condicional baseada no resultado
**PODE usar FlowNavigationService:** Se tiver navegação condicional

**Fluxo:**
1. Executa comando configurado
2. Se comando tem navegação condicional, usa FlowNavigationService
3. Senão, usa `next_step` padrão

### 3.5 CONDITION Step
**Função:** Avaliação condicional para branching
**Navegação:** Sempre condicional
**USA FlowNavigationService:** Para determinar branch correto

**Fluxo:**
1. Avalia condições configuradas
2. FlowNavigationService determina próximo step baseado no resultado
3. Usa `target_step_identifier` da condição que passou

### 3.6 WEBHOOK Step
**Função:** Chamar APIs externas
**Navegação:** Pode ser condicional baseada na resposta
**PODE usar FlowNavigationService:** Se tiver navegação condicional

**Fluxo:**
1. Faz chamada HTTP para URL configurada
2. Se webhook tem navegação condicional baseada na resposta, usa FlowNavigationService
3. Senão, usa `next_step` padrão

### 3.7 DELAY Step
**Função:** Introduzir delay temporal
**Navegação:** Sempre linear após delay
**Não precisa de FlowNavigationService:** Navegação é sempre `next_step`

**Fluxo:**
1. Aguarda tempo configurado
2. Opcionalmente envia mensagem durante delay
3. Avança para `next_step`

## 4. Implementação Prática

### 4.1 Ordem de Implementação
1. **PASSO 1:** Corrigir FlowNavigationService (2h)
2. **PASSO 2:** Reverter ProcessWebhookMessage (30min)
3. **PASSO 3:** Integrar em InteractiveStepProcessor (1h)
4. **PASSO 4:** Substituir ConditionalNavigationService (1h)

**TOTAL:** ~4.5 horas de trabalho focado

### 4.2 Critérios de Sucesso
- [ ] FlowNavigationService funciona para botões condicionais
- [ ] ProcessWebhookMessage volta a ter responsabilidade única
- [ ] InteractiveStepProcessor usa navegação unificada
- [ ] ConditionalNavigationService pode ser removido
- [ ] Todos os tipos de step continuam funcionando
- [ ] Navegação position → step_id funciona consistentemente

### 4.3 Testes de Validação
- [ ] Botão com `internal_type='condition'` navega corretamente
- [ ] Botão normal usa `next_step` padrão
- [ ] Steps MESSAGE/INPUT/DELAY funcionam como antes
- [ ] Steps INTERACTIVE usam nova navegação
- [ ] Steps CONDITION/COMMAND/WEBHOOK podem usar navegação condicional

## 5. Benefícios da Abordagem

1. **Mínima disrupção:** Não quebra o que já funciona
2. **Responsabilidades claras:** Cada component tem função específica
3. **Navegação unificada:** FlowNavigationService centraliza lógica complexa
4. **Flexibilidade:** Processors podem escolher usar ou não navegação unificada
5. **Manutenibilidade:** Código mais limpo e organizados
