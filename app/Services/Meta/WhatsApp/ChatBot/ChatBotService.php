<?php

namespace App\Services\Meta\WhatsApp\ChatBot;

use App\Domains\ChatBot\PhoneNumber;
use App\Domains\ChatBot\Flow;
use App\Domains\ChatBot\Conversation;
use App\Domains\ChatBot\Step;
use App\Domains\Inventory\Client;
use App\Domains\Organization;
use App\Helpers\DBLog;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessWebhookMessage;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\FindOrCreateClient;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\FindOrCreateConversation;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessFlowStep;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\SendWhatsAppResponse;
use Exception;

class ChatBotService
{
    protected ProcessWebhookMessage $processWebhookMessage;
    protected FindOrCreateClient $findOrCreateClient;
    protected FindOrCreateConversation $findOrCreateConversation;
    protected ProcessFlowStep $processFlowStep;
    protected SendWhatsAppResponse $sendWhatsAppResponse;

    public function __construct(
        ProcessWebhookMessage $processWebhookMessage,
        FindOrCreateClient $findOrCreateClient,
        FindOrCreateConversation $findOrCreateConversation,
        ProcessFlowStep $processFlowStep,
        SendWhatsAppResponse $sendWhatsAppResponse
    ) {
        $this->processWebhookMessage = $processWebhookMessage;
        $this->findOrCreateClient = $findOrCreateClient;
        $this->findOrCreateConversation = $findOrCreateConversation;
        $this->processFlowStep = $processFlowStep;
        $this->sendWhatsAppResponse = $sendWhatsAppResponse;
    }

    /**
     * Main entry point for processing WhatsApp webhook messages
     *
     * @param array $webhookData
     * @return array
     * @throws Exception
     */
    public function processWebhook(array $webhookData, PhoneNumber $phoneNumber): array
    {
        try {
            DBLog::log(
                "ChatBotService::processWebhook - Processing webhook",
                "WhatsApp::ChatBotService",
                null,
                null,
                ['webhook_data' => $webhookData]
            );

            // First, extract basic message data
            $basicMessageData = $this->processWebhookMessage->performLegacy($webhookData);
            DBLog::log(
                "ChatBotService::processWebhook - Basic message data extracted",
                "WhatsApp::ChatBotService",
                null,
                null,
                ['message_data' => $basicMessageData]
            );

            $client = $this->findOrCreateClient->perform($basicMessageData);
            DBLog::log(
                "ChatBotService::processWebhook - Client found or created",
                "WhatsApp::ChatBotService",
                null,
                null,
                ['client' => $client->toArray()]
            );

            $conversation = $this->findOrCreateConversation->perform($basicMessageData, $client, $phoneNumber);
            DBLog::log(
                "ChatBotService::processWebhook - Conversation found or created",
                "WhatsApp::ChatBotService",
                null,
                null,
                ['conversation' => $conversation->toArray()]
            );

            // Now process with navigation using the conversation
            $messageDataWithNavigation = $this->processWebhookMessage->perform($webhookData, $conversation);
            DBLog::log(
                "ChatBotService::processWebhook - Navigation processed",
                "WhatsApp::ChatBotService",
                null,
                null,
                ['navigation_result' => $messageDataWithNavigation]
            );

            $stepResult = $this->processFlowStep->perform($conversation, $basicMessageData);
            DBLog::log(
                "ChatBotService::processWebhook - Flow step processed",
                "WhatsApp::ChatBotService",
                null,
                null,
                ['step_result' => $stepResult]
            );

            $response = $this->sendWhatsAppResponse->perform($stepResult, $conversation);

            return [
                'success' => true,
                'conversation_id' => $conversation->id,
                'client_id' => $client->id,
                'step_result' => $stepResult,
                'response' => $response
            ];

        } catch (Exception $e) {
            DBLog::logError(
                "ChatBotService::processWebhook - Error processing webhook: " . $e->getMessage(),
                "WhatsApp::ChatBotService",
                null,
                null,
                ['webhook_data' => $webhookData, 'error' => $e->getMessage()]
            );

            throw $e;
        }
    }

    /**
     * Get flow for phone number (with fallback to organization default)
     *
     * @param PhoneNumber $phoneNumber
     * @return Flow|null
     */
    public function getFlowForPhoneNumber(PhoneNumber $phoneNumber): ?Flow
    {
        // First try phone number's assigned flow
        if ($phoneNumber->flow) {
            return $phoneNumber->flow;
        }

        // Fallback to organization's default flow
        if ($phoneNumber->organization && $phoneNumber->organization->default_flow_id) {
            // TODO: Load organization's default flow
            return null; // Will implement this when we have flow repository
        }

        return null;
    }
}
