<?php

namespace Tests\Feature\Services\Meta\WhatsApp\ChatBot\Services;

use App\Domains\ChatBot\Step;
use App\Domains\ChatBot\StepNavigation;
use App\Domains\ChatBot\Component;
use App\Domains\ChatBot\Button;
use App\Repositories\StepRepository;
use App\Repositories\StepNavigationRepository;
use App\Services\Meta\WhatsApp\ChatBot\Services\FlowNavigationService;
use Tests\TestCase;
use Mockery;
use Carbon\Carbon;

class FlowNavigationServiceTest extends TestCase
{
    protected FlowNavigationService $service;
    protected $stepRepository;
    protected $stepNavigationRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->stepRepository = Mockery::mock(StepRepository::class);
        $this->stepNavigationRepository = Mockery::mock(StepNavigationRepository::class);

        $this->service = new FlowNavigationService(
            $this->stepRepository,
            $this->stepNavigationRepository
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function can_be_instantiated_via_app_make()
    {
        $service = app()->make(FlowNavigationService::class);
        $this->assertInstanceOf(FlowNavigationService::class, $service);
    }

    /** @test */
    public function getNextStep_returns_null_when_no_navigation_possible()
    {
        $currentStep = $this->createStep(1, 1, null, null);

        $result = $this->service->getNextStep($currentStep, 'test input');

        $this->assertNull($result);
    }

    /** @test */
    public function getNextStep_uses_navigation_rules_when_available()
    {
        $currentStep = $this->createStep(1, 1, 2, null);
        $nextStep = $this->createStep(2, 1, null, null);

        // Mock navigation rule matching
        $navigationRule = $this->createStepNavigation(1, 'button_click', ['button_id' => 'btn1'], 'step_2');
        $currentStep->stepNavigations = [$navigationRule];

        $this->stepRepository
            ->shouldReceive('findByIdentifierInFlow')
            ->with('step_2', 1)
            ->andReturn($nextStep);

        $this->stepRepository
            ->shouldReceive('fetchById')
            ->with(2)
            ->andReturn($nextStep);

        $result = $this->service->getNextStep($currentStep, 'btn1');

        $this->assertEquals($nextStep, $result);
    }

    /** @test */
    public function getNextStep_falls_back_to_default_next_step()
    {
        $currentStep = $this->createStep(1, 1, 2, null);
        $nextStep = $this->createStep(2, 1, null, null);

        $this->stepRepository
            ->shouldReceive('findByPositionInFlow')
            ->with(2, 1)
            ->andReturn($nextStep);

        $result = $this->service->getNextStep($currentStep, 'any input');

        $this->assertEquals($nextStep, $result);
    }

    /** @test */
    public function convertPositionToStepId_returns_correct_step()
    {
        $expectedStep = $this->createStep(2, 1, null, null);

        $this->stepRepository
            ->shouldReceive('findByPositionInFlow')
            ->with(2, 1)
            ->andReturn($expectedStep);

        $result = $this->service->convertPositionToStepId(2, 1);

        $this->assertEquals($expectedStep, $result);
    }

    /** @test */
    public function convertPositionToStepId_returns_null_when_step_not_found()
    {
        $this->stepRepository
            ->shouldReceive('findByPositionInFlow')
            ->with(999, 1)
            ->andReturn(null);

        $result = $this->service->convertPositionToStepId(999, 1);

        $this->assertNull($result);
    }

    /** @test */
    public function hasNavigationRules_returns_true_when_step_has_rules()
    {
        $currentStep = $this->createStep(1, 1, 2, null);
        $navigationRule = $this->createStepNavigation(1, 'button_click', ['button_id' => 'btn1'], 'step_2');
        $currentStep->stepNavigations = [$navigationRule];

        $result = $this->service->hasNavigationRules($currentStep);

        $this->assertTrue($result);
    }

    /** @test */
    public function hasNavigationRules_returns_false_when_step_has_no_rules()
    {
        $currentStep = $this->createStep(1, 1, 2, null);
        $currentStep->stepNavigations = [];

        $result = $this->service->hasNavigationRules($currentStep);

        $this->assertFalse($result);
    }

    /** @test */
    public function hasConditionalNavigation_returns_true_when_step_has_navigation_rules()
    {
        $currentStep = $this->createStep(1, 1, 2, null);
        $navigationRule = $this->createStepNavigation(1, 'button_click', ['button_id' => 'btn1'], 'step_2');
        $currentStep->stepNavigations = [$navigationRule];

        $result = $this->service->hasConditionalNavigation($currentStep);

        $this->assertTrue($result);
    }

    /** @test */
    public function hasConditionalNavigation_returns_true_when_step_has_conditional_buttons()
    {
        $currentStep = $this->createStep(1, 1, 2, null);
        $currentStep->stepNavigations = [];

        // Create component with conditional button
        $button = $this->createButton(1, 'Test Button', 'condition');
        $component = $this->createComponent(1, [$button]);
        $currentStep->component = $component;

        $result = $this->service->hasConditionalNavigation($currentStep);

        $this->assertTrue($result);
    }

    /** @test */
    public function hasConditionalNavigation_returns_false_when_no_conditional_elements()
    {
        $currentStep = $this->createStep(1, 1, 2, null);
        $currentStep->stepNavigations = [];

        $result = $this->service->hasConditionalNavigation($currentStep);

        $this->assertFalse($result);
    }

    /**
     * Helper method to create a Step domain object
     */
    protected function createStep(int $id, int $flowId, ?int $nextStep, ?int $earlierStep): Step
    {
        return new Step(
            id: $id,
            organization_id: 1,
            flow_id: $flowId,
            step: "step_{$id}",
            type: null,
            step_type: null,
            position: $id,
            next_step: $nextStep,
            earlier_step: $earlierStep,
            is_initial_step: false,
            is_ending_step: false,
            configuration: null,
            navigation_rules: null,
            timeout_seconds: null,
            json: null,
            input: null,
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            flow: null,
            component: null,
            stepNavigations: []
        );
    }

    /**
     * Helper method to create a StepNavigation domain object
     */
    protected function createStepNavigation(int $stepId, string $conditionType, array $conditionData, string $targetStepIdentifier): StepNavigation
    {
        return new StepNavigation(
            id: 1,
            organization_id: 1,
            step_id: $stepId,
            condition_type: $conditionType,
            condition_data: $conditionData,
            target_step_identifier: $targetStepIdentifier,
            priority: 0,
            is_active: true,
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    /**
     * Helper method to create a Button domain object
     */
    protected function createButton(int $id, string $text, ?string $internalType = null): Button
    {
        return new Button(
            id: $id,
            organization_id: 1,
            text: $text,
            type: 'QUICK_REPLY',
            internal_type: $internalType,
            internal_data: null,
            callback_data: null,
            json: null,
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    /**
     * Helper method to create a Component domain object
     */
    protected function createComponent(int $id, array $buttons = []): Component
    {
        return new Component(
            id: $id,
            organization_id: 1,
            step_id: null,
            template_id: null,
            name: 'Test Component',
            type: 'interactive',
            sub_type: null,
            index: null,
            text: 'Test component',
            format: null,
            json: null,
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            step: null,
            template: null,
            buttons: $buttons,
            parameters: []
        );
    }
}
