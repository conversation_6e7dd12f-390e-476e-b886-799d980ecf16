<?php

namespace App\Services\Meta\WhatsApp\ChatBot;

use App\Domains\ChatBot\Interaction;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\ChatBot\Flow;
use App\Domains\ChatBot\Conversation;
use App\Domains\ChatBot\Step;
use App\Domains\Inventory\Client;
use App\Domains\Organization;
use App\Helpers\DBLog;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ExtractMessageData;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\GetFlowByPhoneNumber;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessWebhookMessage;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\FindOrCreateClient;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\FindOrCreateConversation;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessFlowStep;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\SendWhatsAppResponse;
use Exception;

class ChatBotService
{
    protected ProcessWebhookMessage $processWebhookMessage;
    protected FindOrCreateClient $findOrCreateClient;
    protected FindOrCreateConversation $findOrCreateConversation;
    protected ProcessFlowStep $processFlowStep;
    protected SendWhatsAppResponse $sendWhatsAppResponse;
    protected ExtractMessageData $extractMessageData;
    protected GetFlowByPhoneNumber $getFlowByPhoneNumber;

    public Flow $flow;
    public PhoneNumber $phoneNumber;
    public Client $client;
    public Conversation $conversation;
    public Interaction $interaction;

    public function __construct(
        ProcessWebhookMessage $processWebhookMessage,
        FindOrCreateClient $findOrCreateClient,
        FindOrCreateConversation $findOrCreateConversation,
        ProcessFlowStep $processFlowStep,
        SendWhatsAppResponse $sendWhatsAppResponse,
        ExtractMessageData $extractMessageData,
        GetFlowByPhoneNumber $getFlowByPhoneNumber
    ) {
        $this->processWebhookMessage = $processWebhookMessage;
        $this->findOrCreateClient = $findOrCreateClient;
        $this->findOrCreateConversation = $findOrCreateConversation;
        $this->processFlowStep = $processFlowStep;
        $this->sendWhatsAppResponse = $sendWhatsAppResponse;
        $this->extractMessageData = $extractMessageData;
        $this->getFlowByPhoneNumber = $getFlowByPhoneNumber;
    }

    /**
     * Main entry point for processing WhatsApp webhook messages
     *
     * @param array $webhookData
     * @return array
     * @throws Exception
     */
    public function processWebhook(array $webhookData, PhoneNumber $phoneNumber): array
    {
        try {

            DBLog::log(
                "ChatBotService::processWebhook - Processing webhook",
                "WhatsApp::ChatBotService",
                null,
                null,
                ['webhook_data' => $webhookData]
            );

            $messageData = $this->extractMessageData->perform($webhookData);

            $this->phoneNumber = $phoneNumber;

            $this->flow = $this->getFlowByPhoneNumber->perform($phoneNumber);

            $this->client = $this->findOrCreateClient->perform($messageData);

            $this->conversation = $this->findOrCreateConversation->perform($messageData, $this->client, $phoneNumber);

            $messageDataWithNavigation = $this->processWebhookMessage->perform($messageData, $this->conversation);

            DBLog::log(
                "ChatBotService::processWebhook - Navigation processed",
                "WhatsApp::ChatBotService",
                null,
                null,
                ['navigation_result' => $messageDataWithNavigation]
            );

            $stepResult = $this->processFlowStep->perform($this->conversation, $messageData);
            DBLog::log(
                "ChatBotService::processWebhook - Flow step processed",
                "WhatsApp::ChatBotService",
                null,
                null,
                ['step_result' => $stepResult]
            );

            $response = $this->sendWhatsAppResponse->perform($stepResult, $this->conversation);

            return [
                'success' => true,
                'conversation_id' => $this->conversation->id,
                'client_id' => $this->client->id,
                'step_result' => $stepResult,
                'response' => $response
            ];

        } catch (Exception $e) {
            DBLog::logError(
                "ChatBotService::processWebhook - Error processing webhook: " . $e->getMessage(),
                "WhatsApp::ChatBotService",
                null,
                null,
                ['webhook_data' => $webhookData, 'error' => $e->getMessage()]
            );

            throw $e;
        }
    }
}
