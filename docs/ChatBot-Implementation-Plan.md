# ChatBot Flow - Plano de Implementação Completo

## 1. Flow Completo da Pizzaria (Para Teste Final)

### 1.1 JSON Completo Validado
```json
{
  "flow": {
    "name": "Fluxo Pizzaria",
    "description": "Fluxo completo para pedidos de pizza com escolha de tamanho e sabores",
    "is_default_flow": true,
    "inactivity_minutes": 30,
    "ending_conversation_message": "Obrigado por escolher nossa pizzaria! Seu pedido foi registrado.",
    "version": "1.0",
    "status": "active",
    "variables": {
      "pizza_size": null,
      "pizza_flavor": null,
      "total_price": 0,
      "client_name": null
    }
  },
  "steps": [
    {
      "step": "welcome",
      "step_type": "interactive",
      "position": 0,
      "next_step": 1,
      "is_initial_step": true,
      "is_ending_step": false,
      "component": {
        "name": "Welcome Component",
        "type": "interactive",
        "text": "🍕 Bem-vindo à nossa Pizzaria! {{client.name}}, vamos fazer seu pedido?",
        "format": "TEXT",
        "buttons": [
          {
            "text": "Fazer Pedido",
            "type": "reply",
            "internal_type": "navigation",
            "internal_data": "next_step"
          }
        ]
      }
    },
    {
      "step": "choose_size",
      "step_type": "interactive",
      "position": 1,
      "next_step": 2,
      "earlier_step": 0,
      "is_initial_step": false,
      "is_ending_step": false,
      "component": {
        "name": "Size Selection Component",
        "type": "interactive",
        "text": "Escolha o tamanho da sua pizza:",
        "format": "TEXT",
        "buttons": [
          {
            "text": "P - R$ 25,00",
            "type": "reply",
            "internal_type": "condition",
            "internal_data": "size_p",
            "callback_data": "{\"size\":\"P\",\"price\":25,\"next_position\":2}"
          },
          {
            "text": "M - R$ 35,00",
            "type": "reply",
            "internal_type": "condition",
            "internal_data": "size_m",
            "callback_data": "{\"size\":\"M\",\"price\":35,\"next_position\":2}"
          },
          {
            "text": "G - R$ 45,00",
            "type": "reply",
            "internal_type": "condition",
            "internal_data": "size_g",
            "callback_data": "{\"size\":\"G\",\"price\":45,\"next_position\":2}"
          },
          {
            "text": "Voltar",
            "type": "reply",
            "internal_type": "navigation",
            "internal_data": "previous_step"
          }
        ]
      }
    },
    {
      "step": "choose_flavor",
      "step_type": "interactive",
      "position": 2,
      "next_step": 3,
      "earlier_step": 1,
      "is_initial_step": false,
      "is_ending_step": false,
      "component": {
        "name": "Flavor Selection Component",
        "type": "interactive",
        "text": "Escolha o sabor da sua pizza {{pizza.size}}:",
        "format": "TEXT",
        "buttons": [
          {
            "text": "Calabresa",
            "type": "reply",
            "internal_type": "condition",
            "internal_data": "flavor_calabresa",
            "callback_data": "{\"flavor\":\"Calabresa\",\"next_position\":3}"
          },
          {
            "text": "Portuguesa",
            "type": "reply",
            "internal_type": "condition",
            "internal_data": "flavor_portuguesa",
            "callback_data": "{\"flavor\":\"Portuguesa\",\"next_position\":3}"
          },
          {
            "text": "Frango",
            "type": "reply",
            "internal_type": "condition",
            "internal_data": "flavor_frango",
            "callback_data": "{\"flavor\":\"Frango\",\"next_position\":3}"
          },
          {
            "text": "Voltar",
            "type": "reply",
            "internal_type": "navigation",
            "internal_data": "previous_step"
          }
        ]
      }
    },
    {
      "step": "order_summary",
      "step_type": "interactive",
      "position": 3,
      "next_step": 4,
      "earlier_step": 2,
      "is_initial_step": false,
      "is_ending_step": false,
      "component": {
        "name": "Order Summary Component",
        "type": "interactive",
        "text": "📋 Resumo do seu pedido:\n\n🍕 Tamanho: {{pizza.size}}\n🎯 Sabor: {{pizza.flavor}}\n💰 Total: R$ {{pizza.total_price}}\n\nConfirma o pedido?",
        "format": "TEXT",
        "buttons": [
          {
            "text": "Confirmar Pedido",
            "type": "reply",
            "internal_type": "condition",
            "internal_data": "confirm_order",
            "callback_data": "{\"action\":\"confirm\",\"next_position\":4}"
          },
          {
            "text": "Modificar Pedido",
            "type": "reply",
            "internal_type": "navigation",
            "internal_data": "restart_flow"
          },
          {
            "text": "Cancelar Pedido",
            "type": "reply",
            "internal_type": "condition",
            "internal_data": "cancel_order",
            "callback_data": "{\"action\":\"cancel\",\"next_position\":4}"
          }
        ]
      }
    },
    {
      "step": "order_finalized",
      "step_type": "message",
      "position": 4,
      "is_initial_step": false,
      "is_ending_step": true,
      "component": {
        "name": "Order Finalized Component",
        "type": "message",
        "text": "✅ Pedido confirmado com sucesso!\n\n{{client.name}}, sua pizza {{pizza.size}} de {{pizza.flavor}} será preparada em aproximadamente 30 minutos.\n\n📍 Endereço de entrega será confirmado por telefone.\n💰 Total: R$ {{pizza.total_price}}\n\nObrigado pela preferência! 🍕",
        "format": "TEXT"
      }
    }
  ]
}
```

## 2. Plano de Implementação Real

### 2.1 Análise do Ponto de Partida

**Arquivo Chave:** `app/Services/Meta/WhatsApp/ChatBot/UseCases/ProcessWebhookMessage.php`

**Problema Atual:**
- ProcessWebhookMessage processa webhook mas não tem lógica de navegação
- Múltiplos processors conflitantes (InteractiveStepProcessor, etc.)
- Navegação inconsistente entre position e step_id

**Estratégia:**
- Começar refatoração a partir de ProcessWebhookMessage
- Criar FlowNavigationService como componente central
- Substituir lógicas conflitantes por sistema único

### 2.2 Arquivos a Serem Criados

#### 2.2.1 FlowNavigationService (NOVO)
**Arquivo:** `app/Services/Meta/WhatsApp/ChatBot/Services/FlowNavigationService.php`
**Responsabilidade:** Sistema único de navegação entre steps
**Substitui:** ConditionalNavigationService, lógicas em processors

#### 2.2.2 FlowValidationService (NOVO)  
**Arquivo:** `app/Services/Meta/WhatsApp/ChatBot/Services/FlowValidationService.php`
**Responsabilidade:** Validar flows no SaveFullFlow
**Substitui:** Validações espalhadas

#### 2.2.3 StepProcessorFactory (REFATORADO)
**Arquivo:** `app/Services/Meta/WhatsApp/ChatBot/Processors/StepProcessorFactory.php`
**Responsabilidade:** Criar processor correto baseado no step_type
**Substitui:** Lógica atual de seleção de processor

### 2.3 Arquivos a Serem Modificados

#### 2.3.1 ProcessWebhookMessage (PONTO DE PARTIDA)
**Arquivo:** `app/Services/Meta/WhatsApp/ChatBot/UseCases/ProcessWebhookMessage.php`
**Mudanças:**
- Integrar com FlowNavigationService
- Simplificar lógica de processamento
- Padronizar retorno

#### 2.3.2 InteractiveStepProcessor (SIMPLIFICADO)
**Arquivo:** `app/Services/Meta/WhatsApp/ChatBot/Processors/InteractiveStepProcessor.php`
**Mudanças:**
- Remover lógica de navegação (mover para FlowNavigationService)
- Focar apenas em processar interações
- Usar FlowNavigationService para determinar próximo step

#### 2.3.3 SaveFullFlow (MELHORADO)
**Arquivo:** `app/UseCases/ChatBot/Flow/SaveFullFlow.php`
**Mudanças:**
- Integrar FlowValidationService
- Validar flow completo antes de salvar
- Garantir referências corretas

## 3. Implementação Passo a Passo

### PASSO 1: Criar FlowNavigationService
**Objetivo:** Sistema único de navegação
**Tempo:** 1-2 horas
**Arquivos:**
- Criar: `FlowNavigationService.php`
- Teste: `FlowNavigationServiceTest.php`

### PASSO 2: Modificar ProcessWebhookMessage
**Objetivo:** Integrar navegação única
**Tempo:** 1 hora
**Arquivos:**
- Modificar: `ProcessWebhookMessage.php`
- Testar integração

### PASSO 3: Simplificar InteractiveStepProcessor
**Objetivo:** Remover lógicas conflitantes
**Tempo:** 1 hora
**Arquivos:**
- Modificar: `InteractiveStepProcessor.php`
- Remover: `getNextStepForSelection()` complexo

### PASSO 4: Criar FlowValidationService
**Objetivo:** Validar flows no SaveFullFlow
**Tempo:** 2 horas
**Arquivos:**
- Criar: `FlowValidationService.php`
- Modificar: `SaveFullFlow.php`

### PASSO 5: Testar Flow Pizzaria
**Objetivo:** Validar implementação end-to-end
**Tempo:** 2 horas
**Processo:**
- Salvar flow pizzaria via SaveFullFlow
- Testar navegação completa
- Verificar todas as transições

### PASSO 6: Limpeza e Refatoração
**Objetivo:** Remover código legado
**Tempo:** 1 hora
**Arquivos:**
- Remover: ConditionalNavigationService (se não usado)
- Limpar: Lógicas duplicadas
- Documentar: Mudanças realizadas


## 4. Detalhamento Técnico dos Componentes

### 4.1 FlowNavigationService - Especificação

```php
class FlowNavigationService
{
    protected StepRepository $stepRepository;
    
    /**
     * Determina próximo step baseado na interação do usuário
     */
    public function getNextStep(Step $currentStep, string $userInput, array $context = []): ?Step
    {
        // 1. Identifica tipo de input (botão vs texto)
        // 2. Para botões: processa navegação ou condição
        // 3. Para texto: aplica regras de texto livre
        // 4. SEMPRE converte position → step_id
    }
    
    /**
     * Converte position para step_id no mesmo flow
     */
    public function convertPositionToStepId(int $position, int $flowId): ?int
    {
        return $this->stepRepository->findByPositionInFlow($position, $flowId)?->id;
    }
}
```

### 4.2 ProcessWebhookMessage - Modificações Específicas

**ANTES:** Lógica complexa e conflitante
**DEPOIS:** Integração com FlowNavigationService

```php
public function perform(array $webhookData): array
{
    // 1. Extrai dados do webhook (mantém atual)
    $messageData = $this->extractMessageData($webhookData);
    
    // 2. Busca conversa atual
    $conversation = $this->getOrCreateConversation($messageData);
    
    // 3. NOVA LÓGICA: Usa FlowNavigationService
    $flowNavigationService = app()->make(FlowNavigationService::class);
    $nextStep = $flowNavigationService->getNextStep(
        $conversation->currentStep,
        $messageData["content"],
        ["conversation" => $conversation]
    );
    
    // 4. Retorna resultado padronizado
    return [
        "conversation" => $conversation,
        "current_step" => $conversation->currentStep,
        "next_step" => $nextStep,
        "move_to_next" => $nextStep !== null,
        "message_data" => $messageData
    ];
}
```

### 4.3 InteractiveStepProcessor - Simplificação

**REMOVER:** getNextStepForSelection() completamente
**SIMPLIFICAR:** Focar apenas em processar interação

```php
public function process(Step $step, WhatsAppInteraction $interaction): array
{
    $selectedOption = $this->getSelectedOption($interaction);
    
    if ($selectedOption) {
        // Botão clicado - FlowNavigationService decide próximo
        return [
            "type" => "interactive",
            "step_id" => $step->id,
            "action" => "process_selection",
            "selection" => $selectedOption,
            "move_to_next" => true
        ];
    } else {
        // Primeira vez - mostra opções
        return [
            "type" => "interactive", 
            "step_id" => $step->id,
            "action" => "show_options",
            "message" => $step->component->text,
            "options" => $this->buildOptions($step),
            "move_to_next" => false
        ];
    }
}
```

## 5. Critérios de Teste Final

### 5.1 Cenário Completo da Pizzaria

1. **Salvar Flow:** SaveFullFlow com JSON completo
2. **Iniciar:** Cliente envia "Oi" → Step welcome
3. **Nav 1:** Clica "Fazer Pedido" → Step choose_size (position 1)
4. **Nav 2:** Clica "M - R$ 35,00" → Step choose_flavor (position 2)
5. **Nav 3:** Clica "Calabresa" → Step order_summary (position 3)
6. **Nav 4:** Clica "Confirmar" → Step order_finalized (position 4)
7. **Final:** Conversa fechada automaticamente

### 5.2 Validações Obrigatórias

- ✅ Position → Step_ID sempre correto
- ✅ Variáveis atualizadas (size, flavor, price)
- ✅ Botão "Voltar" funciona
- ✅ Logs mostram navegação correta
- ✅ Conversa finaliza adequadamente

## 6. Cronograma

**Total:** 8-10 horas
- **Dia 1:** FlowNavigationService + ProcessWebhookMessage (4h)
- **Dia 2:** InteractiveStepProcessor + FlowValidationService (3h)
- **Dia 3:** Teste completo + Limpeza (2h)

**Resultado:** ChatBot 100% funcional com flow pizzaria validado.

