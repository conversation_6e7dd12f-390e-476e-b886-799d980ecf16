<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases;

use App\Domains\Inventory\Client;
use App\Factories\Inventory\ClientFactory;
use App\Repositories\ClientRepository;
use Exception;

class FindOrCreateClient
{
    protected ClientRepository $clientRepository;
    protected ClientFactory $clientFactory;

    public function __construct(
        ClientRepository $clientRepository,
        ClientFactory $clientFactory
    ) {
        $this->clientRepository = $clientRepository;
        $this->clientFactory = $clientFactory;
    }

    /**
     * Find existing client or create new one from WhatsApp message data
     *
     * @param array $messageData
     * @return Client
     * @throws Exception
     */
    public function perform(array $messageData): Client
    {
        $phoneNumber = $messageData['from'];
        $organizationId = $this->getOrganizationIdFromPhoneNumber($messageData);

        // Try to find existing client by phone number
        $existingClient = $this->findClientByPhone($phoneNumber, $organizationId);

        if ($existingClient) {
            // Update client name if we have better information from What<PERSON><PERSON><PERSON>
            return $this->updateClientIfNeeded($existingClient, $messageData);
        }

        // Create new client
        return $this->createNewClient($messageData, $organizationId);
    }

    /**
     * Find client by phone number within organization
     */
    protected function findClientByPhone(string $phoneNumber, int $organizationId): ?Client
    {
        // Try exact match first
        $client = $this->clientRepository->findByPhoneAndOrganization($phoneNumber, $organizationId);

        if ($client) {
            return $client;
        }

        // Try with different phone number formats
        $phoneVariations = $this->generatePhoneVariations($phoneNumber);

        foreach ($phoneVariations as $variation) {
            $client = $this->clientRepository->findByPhoneAndOrganization($variation, $organizationId);
            if ($client) {
                return $client;
            }
        }
        return null;
    }

    /**
     * Update client information if we have better data from WhatsApp
     */
    protected function updateClientIfNeeded(Client $client, array $messageData): Client
    {
        $needsUpdate = false;

        // Update name if client doesn't have one or WhatsApp provides a better one
        $whatsappName = $messageData['contact_name'] ?? $messageData['profile_name'];
        if ($whatsappName && (empty($client->name) || $client->name === $client->phone)) {
            $client->name = $whatsappName;
            $needsUpdate = true;
        }

        // Update whatsapp_from if not set for future efficiency
        $whatsappFrom = $messageData['from'];
        if (empty($client->whatsapp_from) && $whatsappFrom) {
            $client->whatsapp_from = $whatsappFrom;
            $needsUpdate = true;
        }

        if ($needsUpdate) {
            return $this->clientRepository->save($client);
        }

        return $client;
    }

    /**
     * Create new client from WhatsApp message data
     */
    protected function createNewClient(array $messageData, int $organizationId): Client
    {
        $phoneNumber = $messageData['from'];
        $name = $messageData['contact_name'] ??
                $messageData['profile_name'] ??
                "WhatsApp User {$phoneNumber}";

        // Create client domain object
        $client = new Client(
            id: null,
            organization_id: $organizationId,
            name: $name,
            phone: $phoneNumber,
            whatsapp_from: $phoneNumber, // WhatsApp number
            email: null,
            profession: null,
            birthdate: null,
            cpf: null,
            cnpj: null,
            service: null,
            address: null,
            number: null,
            neighborhood: null,
            cep: null,
            complement: null,
            civil_state: null,
            description: "Created from WhatsApp: {$messageData['message_id']}",
            created_at: null,
            updated_at: null,
            organization: null,
            asaas: null
        );

        return $this->clientRepository->store($client);
    }

    /**
     * Get organization ID from phone number metadata
     */
    protected function getOrganizationIdFromPhoneNumber(array $messageData): int
    {
        $phoneNumberId = $messageData['phone_number_id'];

        if (!$phoneNumberId) {
            throw new Exception('Cannot determine organization: missing phone_number_id in webhook');
        }

        // Find organization by phone number ID
        $phoneNumber = \App\Models\PhoneNumber::where('whatsapp_phone_number_id', $phoneNumberId)->first();

        if (!$phoneNumber) {
            throw new Exception("Phone number not found for ID: {$phoneNumberId}");
        }

        return $phoneNumber->organization_id;
    }

    /**
     * Generate phone number variations for matching
     */
    protected function generatePhoneVariations(string $phoneNumber): array
    {
        $variations = [];

        // Remove all non-digits
        $digitsOnly = preg_replace('/[^0-9]/', '', $phoneNumber);
        $variations[] = $digitsOnly;

        // If has 13 digits (55 + DDD + number), create variations
        if (strlen($digitsOnly) === 13) {
            // Without country code: DDD + number
            $variations[] = substr($digitsOnly, 2);

            // Only number: without DDD
            $variations[] = substr($digitsOnly, 4);
        }

        // If has 12 digits (55 + DDD + number without 9), create variations
        if (strlen($digitsOnly) === 12) {
            // Without country code: DDD + number
            $variations[] = substr($digitsOnly, 2);

            // Only number: without DDD
            $variations[] = substr($digitsOnly, 4);

            // Add 9 to make it 11 digits (modern mobile format)
            $dddPart = substr($digitsOnly, 2, 2); // DDD (79)
            $numberPart = substr($digitsOnly, 4); // Number part (81166640)
            $with9 = $dddPart . '9' . $numberPart; // 79981166640
            $variations[] = $with9;
            $variations[] = '55' . $with9; // 5579981166640
        }

        // If has 11 digits (DDD + number), create variations
        if (strlen($digitsOnly) === 11) {
            // With country code
            $variations[] = '55' . $digitsOnly;

            // Only number: without DDD
            $variations[] = substr($digitsOnly, 2);

            // Remove 9 to make it 10 digits (old mobile format)
            if (substr($digitsOnly, 2, 1) === '9') {
                $without9 = substr($digitsOnly, 0, 2) . substr($digitsOnly, 3);
                $variations[] = $without9;
                $variations[] = '55' . $without9;
            }
        }

        // If has 10 digits (DDD + number without 9), create variations
        if (strlen($digitsOnly) === 10) {
            // With country code
            $variations[] = '55' . $digitsOnly;

            // Only number: without DDD
            $variations[] = substr($digitsOnly, 2);

            // Add 9 to make it 11 digits (modern mobile format)
            $with9 = substr($digitsOnly, 0, 2) . '9' . substr($digitsOnly, 2);
            $variations[] = $with9;
            $variations[] = '55' . $with9;
        }

        // Add formatting with parentheses and dash
        foreach ($variations as $variation) {
            if (strlen($variation) === 11) {
                $formatted = '(' . substr($variation, 0, 2) . ') ' .
                           substr($variation, 2, 5) . '-' .
                           substr($variation, 7);
                $variations[] = $formatted;
            }
            if (strlen($variation) === 10) {
                $formatted = '(' . substr($variation, 0, 2) . ') ' .
                           substr($variation, 2, 4) . '-' .
                           substr($variation, 6);
                $variations[] = $formatted;
            }
        }

        return array_unique($variations);
    }
}
