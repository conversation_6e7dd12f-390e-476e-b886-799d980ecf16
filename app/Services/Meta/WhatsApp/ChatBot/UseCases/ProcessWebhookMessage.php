<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases;

use App\Services\Meta\WhatsApp\ChatBot\Services\FlowNavigationService;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Repositories\StepRepository;
use Exception;

/**
 * Process webhook message and determine navigation
 *
 * This use case now integrates with FlowNavigationService to provide
 * unified navigation logic for ChatBot flows.
 */
class ProcessWebhookMessage
{
    protected FlowNavigationService $flowNavigationService;
    protected StepRepository $stepRepository;
    protected ExtractMessageData $extractMessageData;

    public function __construct(
        FlowNavigationService $flowNavigationService,
        StepRepository $stepRepository,
        ExtractMessageData $extractMessageData
    ) {
        $this->flowNavigationService = $flowNavigationService;
        $this->stepRepository = $stepRepository;
        $this->extractMessageData = $extractMessageData;
    }

    /**
     * Process webhook message data and determine navigation
     *
     * @param array $messageData
     * @param WhatsAppConversation|null $conversation
     * @return array
     * @throws Exception
     */
    public function perform(array $messageData, ?WhatsAppConversation $conversation = null): array
    {
        if (!$conversation || !$conversation->current_step) {
            return array_merge($messageData, [
                'conversation' => $conversation,
                'current_step' => null,
                'next_step' => null,
                'move_to_next' => false,
                'navigation_applied' => false
            ]);
        }

        $context = [
            'conversation' => $conversation,
            'organization_id' => $conversation->organization_id
        ];

        $nextStep = $this->flowNavigationService->getNextStep(
            $conversation->current_step,
            $messageData['content'] ?? '',
            $context
        );

        return array_merge($messageData, [
            'conversation' => $conversation,
            'current_step' => $conversation->current_step,
            'next_step' => $nextStep,
            'move_to_next' => $nextStep !== null,
            'navigation_applied' => true
        ]);
    }

}
