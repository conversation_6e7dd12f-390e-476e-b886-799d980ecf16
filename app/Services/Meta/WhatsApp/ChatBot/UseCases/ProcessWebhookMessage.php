<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases;

use App\Services\Meta\WhatsApp\ChatBot\Services\FlowNavigationService;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Repositories\StepRepository;
use Exception;

/**
 * Process webhook message and determine navigation
 *
 * This use case now integrates with FlowNavigationService to provide
 * unified navigation logic for ChatBot flows.
 */
class ProcessWebhookMessage
{
    protected FlowNavigationService $flowNavigationService;
    protected StepRepository $stepRepository;

    public function __construct(
        FlowNavigationService $flowNavigationService,
        StepRepository $stepRepository
    ) {
        $this->flowNavigationService = $flowNavigationService;
        $this->stepRepository = $stepRepository;
    }

    /**
     * Process webhook message data and determine navigation
     *
     * @param array $webhookData
     * @param WhatsAppConversation|null $conversation
     * @return array
     * @throws Exception
     */
    public function perform(array $webhookData, ?WhatsAppConversation $conversation = null): array
    {
        // 1. Extract and validate message data (keep existing logic)
        $messageData = $this->extractMessageData($webhookData);

        // 2. If no conversation provided, return basic message data
        if (!$conversation || !$conversation->current_step) {
            return array_merge($messageData, [
                'conversation' => $conversation,
                'current_step' => null,
                'next_step' => null,
                'move_to_next' => false,
                'navigation_applied' => false
            ]);
        }

        // 3. Use FlowNavigationService to determine next step
        $context = [
            'conversation' => $conversation,
            'organization_id' => $conversation->organization_id
        ];

        $nextStep = $this->flowNavigationService->getNextStep(
            $conversation->current_step,
            $messageData['content'] ?? '',
            $context
        );

        // 4. Return standardized result
        return array_merge($messageData, [
            'conversation' => $conversation,
            'current_step' => $conversation->current_step,
            'next_step' => $nextStep,
            'move_to_next' => $nextStep !== null,
            'navigation_applied' => true
        ]);
    }

    /**
     * Extract message data from webhook (existing logic)
     */
    protected function extractMessageData(array $webhookData): array
    {
        $message = $webhookData['message'] ?? [];
        $metadata = $webhookData['metadata'] ?? [];
        $contacts = $webhookData['contacts'] ?? [];

        if (empty($message['from'])) {
            throw new Exception('Missing sender phone number in webhook data');
        }

        if (empty($message['id'])) {
            throw new Exception('Missing message ID in webhook data');
        }

        $fromNumber = $message['from'];
        $contactInfo = $this->extractContactInfo($contacts, $message['from']);
        $messageType = $message['type'] ?? 'text';
        $messageContent = $this->extractMessageContent($message);

        return [
            'message_id' => $message['id'],
            'from' => $fromNumber,
            'original_from' => $message['from'],
            'type' => $messageType,
            'content' => $messageContent,
            'timestamp' => $message['timestamp'] ?? time(),
            'contact_name' => $contactInfo['name'] ?? null,
            'profile_name' => $contactInfo['profile_name'] ?? null,
            'phone_number_id' => $metadata['phone_number_id'] ?? null,
            'display_phone_number' => $metadata['display_phone_number'] ?? null,
            'raw_message' => $message,
            'raw_metadata' => $metadata,
            'raw_contacts' => $contacts,
        ];
    }

    /**
     * Legacy method for backward compatibility
     *
     * @deprecated Use perform() with conversation parameter instead
     */
    public function performLegacy(array $webhookData): array
    {
        return $this->extractMessageData($webhookData);
    }

    /**
     * Normalize phone number format
     */
    protected function normalizePhoneNumber(string $phoneNumber): string
    {
        // Remove any non-digit characters
        $cleaned = preg_replace('/[^0-9]/', '', $phoneNumber);

        // Remove leading country codes (common ones)
        $patterns = [
            '/^55/',  // Brazil
            '/^1/',   // US/Canada
            '/^44/',  // UK
            '/^49/',  // Germany
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $cleaned)) {
                $cleaned = preg_replace($pattern, '', $cleaned, 1);
                break;
            }
        }

        return $cleaned;
    }

    /**
     * Extract contact information from contacts array
     */
    protected function extractContactInfo(array $contacts, string $phoneNumber): array
    {
        foreach ($contacts as $contact) {
            if (($contact['wa_id'] ?? null) === $phoneNumber) {
                return [
                    'name' => $contact['profile']['name'] ?? null,
                    'profile_name' => $contact['profile']['name'] ?? null,
                ];
            }
        }

        return [];
    }

    /**
     * Extract message content based on message type
     */
    protected function extractMessageContent(array $message): ?string
    {
        $type = $message['type'] ?? 'text';

        switch ($type) {
            case 'text':
                return $message['text']['body'] ?? null;

            case 'interactive':
                return $this->extractInteractiveContent($message['interactive'] ?? []);

            case 'button':
                return $message['button']['text'] ?? null;

            case 'image':
            case 'document':
            case 'audio':
            case 'video':
                return "Media message: {$type}";

            case 'location':
                $location = $message['location'] ?? [];
                return "Location: {$location['latitude']}, {$location['longitude']}";

            default:
                return "Unsupported message type: {$type}";
        }
    }

    /**
     * Extract content from interactive messages (buttons, lists)
     */
    protected function extractInteractiveContent(array $interactive): ?string
    {
        $type = $interactive['type'] ?? null;

        switch ($type) {
            case 'button_reply':
                return $interactive['button_reply']['title'] ?? null;

            case 'list_reply':
                return $interactive['list_reply']['title'] ?? null;

            default:
                return "Interactive message: {$type}";
        }
    }
}
