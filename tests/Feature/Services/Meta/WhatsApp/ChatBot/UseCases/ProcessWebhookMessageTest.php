<?php

namespace Tests\Feature\Services\Meta\WhatsApp\ChatBot\UseCases;

use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessWebhookMessage;
use App\Services\Meta\WhatsApp\ChatBot\Services\FlowNavigationService;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Domains\ChatBot\Step;
use App\Repositories\StepRepository;
use Tests\TestCase;
use Mockery;
use Carbon\Carbon;

class ProcessWebhookMessageTest extends TestCase
{
    protected ProcessWebhookMessage $useCase;
    protected $flowNavigationService;
    protected $stepRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->flowNavigationService = Mockery::mock(FlowNavigationService::class);
        $this->stepRepository = Mockery::mock(StepRepository::class);

        $this->useCase = new ProcessWebhookMessage(
            $this->flowNavigationService,
            $this->stepRepository
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function can_be_instantiated_via_app_make()
    {
        $useCase = app()->make(ProcessWebhookMessage::class);
        $this->assertInstanceOf(ProcessWebhookMessage::class, $useCase);
    }

    /** @test */
    public function perform_extracts_basic_message_data_without_conversation()
    {
        $webhookData = [
            'message' => [
                'id' => 'msg_123',
                'from' => '5511999999999',
                'type' => 'text',
                'text' => ['body' => 'Hello']
            ],
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'contacts' => []
        ];

        $result = $this->useCase->perform($webhookData);

        $this->assertEquals('msg_123', $result['message_id']);
        $this->assertEquals('5511999999999', $result['from']);
        $this->assertEquals('text', $result['type']);
        $this->assertEquals('Hello', $result['content']);
        $this->assertNull($result['conversation']);
        $this->assertNull($result['current_step']);
        $this->assertNull($result['next_step']);
        $this->assertFalse($result['move_to_next']);
        $this->assertFalse($result['navigation_applied']);
    }

    /** @test */
    public function perform_applies_navigation_with_conversation()
    {
        $webhookData = [
            'message' => [
                'id' => 'msg_123',
                'from' => '5511999999999',
                'type' => 'text',
                'text' => ['body' => 'Hello']
            ],
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'contacts' => []
        ];

        $currentStep = $this->createStep(1, 1, 2, null);
        $nextStep = $this->createStep(2, 1, null, null);

        $conversation = $this->createConversation(1, 1, $currentStep);

        $this->flowNavigationService
            ->shouldReceive('getNextStep')
            ->with($currentStep, 'Hello', Mockery::type('array'))
            ->andReturn($nextStep);

        $result = $this->useCase->perform($webhookData, $conversation);

        $this->assertEquals('msg_123', $result['message_id']);
        $this->assertEquals('Hello', $result['content']);
        $this->assertEquals($conversation, $result['conversation']);
        $this->assertEquals($currentStep, $result['current_step']);
        $this->assertEquals($nextStep, $result['next_step']);
        $this->assertTrue($result['move_to_next']);
        $this->assertTrue($result['navigation_applied']);
    }

    /** @test */
    public function perform_handles_no_next_step()
    {
        $webhookData = [
            'message' => [
                'id' => 'msg_123',
                'from' => '5511999999999',
                'type' => 'text',
                'text' => ['body' => 'End']
            ],
            'metadata' => [],
            'contacts' => []
        ];

        $currentStep = $this->createStep(1, 1, null, null);
        $conversation = $this->createConversation(1, 1, $currentStep);

        $this->flowNavigationService
            ->shouldReceive('getNextStep')
            ->with($currentStep, 'End', Mockery::type('array'))
            ->andReturn(null);

        $result = $this->useCase->perform($webhookData, $conversation);

        $this->assertNull($result['next_step']);
        $this->assertFalse($result['move_to_next']);
        $this->assertTrue($result['navigation_applied']);
    }

    /** @test */
    public function performLegacy_returns_basic_message_data()
    {
        $webhookData = [
            'message' => [
                'id' => 'msg_123',
                'from' => '5511999999999',
                'type' => 'text',
                'text' => ['body' => 'Hello']
            ],
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'contacts' => []
        ];

        $result = $this->useCase->performLegacy($webhookData);

        $this->assertEquals('msg_123', $result['message_id']);
        $this->assertEquals('5511999999999', $result['from']);
        $this->assertEquals('text', $result['type']);
        $this->assertEquals('Hello', $result['content']);
        $this->assertArrayNotHasKey('conversation', $result);
        $this->assertArrayNotHasKey('next_step', $result);
    }

    /** @test */
    public function perform_throws_exception_for_missing_message_id()
    {
        $webhookData = [
            'message' => [
                'from' => '5511999999999',
                'type' => 'text'
            ]
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Missing message ID in webhook data');

        $this->useCase->perform($webhookData);
    }

    /** @test */
    public function perform_throws_exception_for_missing_from_number()
    {
        $webhookData = [
            'message' => [
                'id' => 'msg_123',
                'type' => 'text'
            ]
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Missing sender phone number in webhook data');

        $this->useCase->perform($webhookData);
    }

    /**
     * Helper method to create a Step domain object
     */
    protected function createStep(int $id, int $flowId, ?int $nextStep, ?int $earlierStep): Step
    {
        return new Step(
            id: $id,
            organization_id: 1,
            flow_id: $flowId,
            step: "step_{$id}",
            type: null,
            step_type: null,
            position: $id,
            next_step: $nextStep,
            earlier_step: $earlierStep,
            is_initial_step: false,
            is_ending_step: false,
            configuration: null,
            navigation_rules: null,
            timeout_seconds: null,
            json: null,
            input: null,
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            flow: null,
            component: null,
            stepNavigations: []
        );
    }

    /**
     * Helper method to create a WhatsAppConversation domain object
     */
    protected function createConversation(int $id, int $organizationId, Step $currentStep): WhatsAppConversation
    {
        return new WhatsAppConversation(
            id: $id,
            organization_id: $organizationId,
            user_id: null,
            client_id: 1,
            flow_id: 1,
            phone_number_id: 1,
            current_step_id: $currentStep->id,
            json: null,
            is_finished: false,
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            whatsapp_contact_name: null,
            whatsapp_profile_name: null,
            whatsapp_metadata: [],
            client: null,
            phone_number: null,
            current_step: $currentStep
        );
    }
}
